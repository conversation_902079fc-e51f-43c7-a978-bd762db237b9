import { NextRequest, NextResponse } from 'next/server';
import { sendExchangeItemReceivedEmail } from '@repo/email';
import { z } from 'zod';

const requestSchema = z.object({
  to: z.string().email(),
  returnNumber: z.string().min(1),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { to, returnNumber } = requestSchema.parse(body);

    const result = await sendExchangeItemReceivedEmail(to, returnNumber);

    if (result.success) {
      return NextResponse.json({ success: true, message: 'Email sent successfully' });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to send email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Email API error:', error);
    return NextResponse.json(
      { success: false, error: 'Invalid request data' },
      { status: 400 }
    );
  }
}
