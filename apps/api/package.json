{"name": "api", "private": true, "scripts": {"dev": "next dev -p 3002 --turbopack", "next": "next dev -p 3002 --turbopack", "build": "next build", "start": "next start", "analyze": "ANALYZE=true pnpm build", "test": "NODE_ENV=test vitest run", "clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@react-email/components": "0.0.36", "@repo/analytics": "workspace:*", "@repo/auth": "workspace:*", "@repo/database": "workspace:*", "@repo/design-system": "workspace:*", "@repo/email": "workspace:*", "@repo/next-config": "workspace:*", "@repo/observability": "workspace:*", "@repo/payments": "workspace:*", "@repo/storage": "workspace:*", "@repo/testing": "workspace:*", "@sentry/nextjs": "^9.13.0", "@t3-oss/env-nextjs": "^0.12.0", "next": "15.3.0", "react": "19.1.0", "react-dom": "19.1.0", "vitest": "^3.1.1", "zod": "^3.24.3"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.14.1", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "concurrently": "^9.1.2", "typescript": "^5.8.3"}}