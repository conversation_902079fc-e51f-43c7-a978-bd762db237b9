'use server';

import prisma from '@repo/database/prisma-client';
import { log } from '@repo/observability/log';

export async function findReturnByReturnNumber(returnNumber: string) {
  log.info(
    `Searching for return request with tracking number: ${returnNumber}`
  );

  try {
    // Find return request by tracking number
    const returnRequest = await prisma.returnRequest.findFirst({
      where: {
        returnNumber,
      },
      select: {
        id: true,
        returnNumber: true,
        processed: true,
        exchangeType: true,
        orderName: true,
        email: true,
        returnItems: {
          select: {
            id: true,
            title: true,
            sku: true,
            barcode: true,
            quantity: true,
          },
        },
      },
    });

    return returnRequest;
  } catch (error) {
    log.error('Error searching for return request:', { error });
    return null;
  }
}

export async function updateReturnRequestStatus(
  returnRequestId: string,
  processed: string
) {
  try {
    // Get current request data before update
    const currentRequest = await prisma.returnRequest.findUnique({
      where: { id: returnRequestId },
      select: {
        processed: true,
        email: true,
        returnNumber: true,
        exchangeType: true,
      },
    });

    if (!currentRequest) {
      throw new Error('Return request not found');
    }

    const updatedRequest = await prisma.returnRequest.update({
      where: { id: returnRequestId },
      data: { processed },
    });

    // Send email if exchange is completed
    if (
      currentRequest.exchangeType === 'exchange' &&
      processed === 'completed' &&
      currentRequest.processed === 'exchange_shipped'
    ) {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002'}/api/email/send`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              type: 'exchange-item-received',
              to: currentRequest.email,
              returnNumber: currentRequest.returnNumber,
            }),
          }
        );

        if (response.ok) {
          log.info(
            `Sent exchange-item-received email to ${currentRequest.email}`
          );
        } else {
          const errorText = await response.text();
          log.error('Failed to send exchange completion email:', {
            error: errorText,
          });
        }
      } catch (emailError) {
        log.error('Error sending exchange completion email:', {
          error: emailError,
        });
      }
    }

    log.info(
      `Updated return request ${returnRequestId} status to ${processed}`
    );
    return updatedRequest;
  } catch (error) {
    log.error('Error updating return request status:', { error });
    throw new Error('Failed to update return request status');
  }
}
