import {
  Body,
  Container,
  Head,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';

type ExchangeRequestManualReceivedEmailProps = {
  readonly returnNumber: string;
};

export const ExchangeRequestManualReceivedEmail = ({
  returnNumber,
}: ExchangeRequestManualReceivedEmailProps) => (
  <Tailwind>
    <Html>
      <Head />
      <Preview>Your exchange request #{returnNumber} is being reviewed</Preview>
      <Body className="bg-zinc-50 font-sans">
        <Container className="mx-auto py-12">
          <Section className="mt-8 rounded-md bg-zinc-200 p-px">
            <Section className="rounded-[5px] bg-white p-8">
              <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                Exchange Request Received
              </Text>
              <Text className="m-0 text-zinc-700">
                Your exchange request #{returnNumber} is being reviewed.
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  </Tailwind>
);

const ExampleExchangeRequestManualReceivedEmail = () => (
  <ExchangeRequestManualReceivedEmail returnNumber="EXC-12345" />
);

export default ExampleExchangeRequestManualReceivedEmail;
