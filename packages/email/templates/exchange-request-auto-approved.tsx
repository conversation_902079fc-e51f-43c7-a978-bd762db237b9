import {
  Body,
  Container,
  Head,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';

type ExchangeRequestAutoApprovedEmailProps = {
  readonly returnNumber: string;
};

export const ExchangeRequestAutoApprovedEmail = ({
  returnNumber,
}: ExchangeRequestAutoApprovedEmailProps) => (
  <Tailwind>
    <Html>
      <Head />
      <Preview>Your exchange request #{returnNumber} has been approved</Preview>
      <Body className="bg-zinc-50 font-sans">
        <Container className="mx-auto py-12">
          <Section className="mt-8 rounded-md bg-zinc-200 p-px">
            <Section className="rounded-[5px] bg-white p-8">
              <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                Exchange Request Approved
              </Text>
              <Text className="m-0 text-zinc-700">
                Your exchange request #{returnNumber} has been approved.
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  </Tailwind>
);

const ExampleExchangeRequestAutoApprovedEmail = () => (
  <ExchangeRequestAutoApprovedEmail returnNumber="EXC-12345" />
);

export default ExampleExchangeRequestAutoApprovedEmail;
