import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';

type ExchangeItemReceivedEmailProps = {
  readonly returnNumber: string;
};

export const ExchangeItemReceivedEmail = ({
  returnNumber,
}: ExchangeItemReceivedEmailProps) => (
  <Tailwind>
    <Html>
      <Head />
      <Preview>Exchange #{returnNumber} is now complete</Preview>
      <Body className="bg-zinc-50 font-sans">
        <Container className="mx-auto py-12">
          <Section className="mt-8 rounded-md bg-zinc-200 p-px">
            <Section className="rounded-[5px] bg-white p-8">
              <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                Exchange Complete
              </Text>
              <Text className="m-0 mb-4 text-zinc-700">
                We've received your returned item for exchange #{returnNumber}.
              </Text>
              <Hr className="my-4" />
              <Text className="m-0 text-zinc-700">
                Your exchange is now complete. Thank you!
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  </Tailwind>
);

const ExampleExchangeItemReceivedEmail = () => (
  <ExchangeItemReceivedEmail
    returnNumber="EXC-12345"
  />
);

export default ExampleExchangeItemReceivedEmail;
