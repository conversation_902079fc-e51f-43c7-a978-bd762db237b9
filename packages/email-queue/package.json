{"name": "@repo/email-queue", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@repo/email": "workspace:*", "@repo/observability": "workspace:*", "@t3-oss/env-nextjs": "^0.12.0", "@upstash/redis": "^1.34.3", "bullmq": "^5.34.0", "ioredis": "^5.4.1", "zod": "^3.24.3"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.14.1", "typescript": "^5.8.3"}}